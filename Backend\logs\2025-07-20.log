{"timestamp":"2025-07-20T05:26:47.262Z","level":"INFO","message":"New user registered: <PERSON><PERSON><PERSON> (<EMAIL>)","data":null}
{"timestamp":"2025-07-20T05:27:26.376Z","level":"INFO","message":"User logged in: <PERSON><PERSON><PERSON> (<EMAIL>)","data":null}
{"timestamp":"2025-07-20T05:27:32.420Z","level":"INFO","message":"User logged out: HKumar","data":null}
{"timestamp":"2025-07-20T05:28:49.456Z","level":"INFO","message":"User logged in: admin (<EMAIL>)","data":null}
{"timestamp":"2025-07-20T05:29:35.449Z","level":"INFO","message":"User logged out: admin","data":null}
{"timestamp":"2025-07-20T05:30:02.503Z","level":"INFO","message":"User logged in: <PERSON><PERSON><PERSON> (<EMAIL>)","data":null}
{"timestamp":"2025-07-20T06:03:12.084Z","level":"ERROR","message":"Registration error:","data":{"message":"Field 'created_at' doesn't have a default value","code":"ER_NO_DEFAULT_FOR_FIELD","errno":1364,"sql":"INSERT INTO user_offered_skills\n               (user_id, skill_id, proficiency_level, status)\n               VALUES (?, ?, 'intermediate', 'approved')","sqlState":"HY000","sqlMessage":"Field 'created_at' doesn't have a default value"}}
{"timestamp":"2025-07-20T06:04:19.723Z","level":"INFO","message":"New user registered: testuser123 (<EMAIL>)","data":null}
