const { User, UserDetails } = require('../models');
const PasswordUtil = require('../utils/password');
const JWTUtil = require('../utils/jwt');
const Logger = require('../utils/logger');
const { executeQuery } = require('../config/database');
const { Op } = require('sequelize');

class AuthController {
  // User registration
  static async register(req, res) {
    try {
      const {
        name,
        email,
        username,
        password,
        bio,
        current_status,
        state,
        country,
        timezone,
        education_level,
        major_field_of_study,
        university_name,
        education_description,
        linkedin_link,
        github_link,
        portfolio_link,
        skillsToTeach = [],
        skillsToLearn = []
      } = req.body;

      // Check if user already exists
      const existingUser = await User.findOne({
        where: {
          [Op.or]: [
            { email: email },
            { username: username }
          ]
        }
      });

      if (existingUser) {
        return res.status(400).json({
          success: false,
          message: 'User with this email or username already exists'
        });
      }

      // Hash password
      const hashedPassword = await PasswordUtil.hashPassword(password);

      // Create user with transaction to ensure both user and user_details are created
      const user = await User.create({
        username,
        email,
        password: hashedPassword,
        status: 'active'
      });

      // Create user details
      await UserDetails.create({
        user_id: user.id,
        name,
        bio: bio || null,
        current_status: current_status || null,
        state: state || null,
        country: country || null,
        timezone: timezone || null,
        education_level: education_level || null,
        major_field_of_study: major_field_of_study || null,
        university_name: university_name || null,
        education_description: education_description || null,
        linkedin_link: linkedin_link || null,
        github_link: github_link || null,
        portfolio_link: portfolio_link || null
      });

      // Handle skills if provided
      console.log('Skills to teach received:', skillsToTeach);
      console.log('Skills to learn received:', skillsToLearn);

      if (skillsToTeach && skillsToTeach.length > 0) {
        for (const skillName of skillsToTeach) {
          // Find skill by name
          const skill = await executeQuery('SELECT id FROM skills WHERE name = ? AND is_active = true', [skillName]);
          if (skill.length > 0) {
            // Add to user_offered_skills with default values
            await executeQuery(
              `INSERT INTO user_offered_skills
               (user_id, skill_id, proficiency_level, status)
               VALUES (?, ?, 'intermediate', 'approved')`,
              [user.id, skill[0].id]
            );
          }
        }
      }

      if (skillsToLearn && skillsToLearn.length > 0) {
        for (const skillName of skillsToLearn) {
          // Find skill by name
          const skill = await executeQuery('SELECT id FROM skills WHERE name = ? AND is_active = true', [skillName]);
          if (skill.length > 0) {
            // Add to user_desired_skills with default values
            await executeQuery(
              `INSERT INTO user_desired_skills
               (user_id, skill_id, interest_level, current_knowledge, status)
               VALUES (?, ?, 'high', 'none', 'active')`,
              [user.id, skill[0].id]
            );
          }
        }
      }

      // Generate JWT token
      const token = JWTUtil.generateToken({
        userId: user.id,
        username: user.username,
        email: user.email
      });

      // Store session
      const ipAddress = req.ip || req.connection.remoteAddress;
      const userAgent = req.get('User-Agent');
      await JWTUtil.storeSession(user.id, token, ipAddress, userAgent);

      // Get complete user data with details for response
      const completeUser = await User.findByPk(user.id, {
        include: [{
          model: UserDetails,
          as: 'details'
        }]
      });

      Logger.info(`New user registered: ${username} (${email})`);

      // Format user data for response (combine user and details)
      const userData = {
        id: completeUser.id,
        username: completeUser.username,
        email: completeUser.email,
        status: completeUser.status,
        is_admin: completeUser.is_admin,
        email_verified: completeUser.email_verified,
        created_at: completeUser.created_at,
        updated_at: completeUser.updated_at,
        name: completeUser.details?.name,
        avatar: completeUser.details?.avatar,
        bio: completeUser.details?.bio,
        current_status: completeUser.details?.current_status,
        state: completeUser.details?.state,
        country: completeUser.details?.country,
        timezone: completeUser.details?.timezone,
        education_level: completeUser.details?.education_level,
        major_field_of_study: completeUser.details?.major_field_of_study,
        university_name: completeUser.details?.university_name,
        education_description: completeUser.details?.education_description,
        linkedin_link: completeUser.details?.linkedin_link,
        github_link: completeUser.details?.github_link,
        portfolio_link: completeUser.details?.portfolio_link,
        rating: completeUser.details?.rating,
        rating_count: completeUser.details?.rating_count
      };

      res.status(201).json({
        success: true,
        message: 'User registered successfully',
        data: {
          user: userData,
          token
        }
      });

    } catch (error) {
      Logger.error('Registration error:', error);
      res.status(500).json({
        success: false,
        message: 'Registration failed',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }

  // User login
  static async login(req, res) {
    try {
      const { username, password } = req.body;

      // Find user by username or email and include user details
      const user = await User.findOne({
        where: {
          [Op.or]: [
            { username: username },
            { email: username }
          ]
        },
        include: [{
          model: UserDetails,
          as: 'details'
        }]
      });

      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'Invalid credentials'
        });
      }

      // Check if account is active
      if (user.status !== 'active') {
        return res.status(403).json({
          success: false,
          message: 'Account is not active. Please contact support.'
        });
      }

      // Verify password
      const isPasswordValid = await PasswordUtil.comparePassword(password, user.password);

      if (!isPasswordValid) {
        return res.status(401).json({
          success: false,
          message: 'Invalid credentials'
        });
      }

      // Generate JWT token
      const token = JWTUtil.generateToken({
        userId: user.id,
        username: user.username,
        email: user.email
      });

      // Store session
      const ipAddress = req.ip || req.connection.remoteAddress;
      const userAgent = req.get('User-Agent');
      await JWTUtil.storeSession(user.id, token, ipAddress, userAgent);

      Logger.info(`User logged in: ${user.username} (${user.email})`);

      // Format user data for response (combine user and details)
      const userData = {
        id: user.id,
        username: user.username,
        email: user.email,
        status: user.status,
        is_admin: user.is_admin,
        email_verified: user.email_verified,
        created_at: user.created_at,
        updated_at: user.updated_at,
        name: user.details?.name,
        avatar: user.details?.avatar,
        bio: user.details?.bio,
        current_status: user.details?.current_status,
        state: user.details?.state,
        country: user.details?.country,
        timezone: user.details?.timezone,
        education_level: user.details?.education_level,
        major_field_of_study: user.details?.major_field_of_study,
        university_name: user.details?.university_name,
        education_description: user.details?.education_description,
        linkedin_link: user.details?.linkedin_link,
        github_link: user.details?.github_link,
        portfolio_link: user.details?.portfolio_link,
        rating: user.details?.rating,
        rating_count: user.details?.rating_count
      };

      res.json({
        success: true,
        message: 'Login successful',
        data: {
          user: userData,
          token
        }
      });

    } catch (error) {
      Logger.error('Login error:', error);
      res.status(500).json({
        success: false,
        message: 'Login failed',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }

  // User logout
  static async logout(req, res) {
    try {
      const { user, token } = req;

      // Remove session from database
      await JWTUtil.removeSession(user.id, token);

      Logger.info(`User logged out: ${user.username}`);

      res.json({
        success: true,
        message: 'Logout successful'
      });

    } catch (error) {
      Logger.error('Logout error:', error);
      res.status(500).json({
        success: false,
        message: 'Logout failed',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }

  // Get current user profile
  static async getProfile(req, res) {
    try {
      const { user } = req;

      // Get complete user profile with details
      const userProfile = await User.findByPk(user.id, {
        include: [{
          model: UserDetails,
          as: 'details'
        }]
      });

      if (!userProfile) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      // Format user data for response
      const userData = {
        id: userProfile.id,
        username: userProfile.username,
        email: userProfile.email,
        status: userProfile.status,
        is_admin: userProfile.is_admin,
        email_verified: userProfile.email_verified,
        created_at: userProfile.created_at,
        updated_at: userProfile.updated_at,
        name: userProfile.details?.name,
        avatar: userProfile.details?.avatar,
        bio: userProfile.details?.bio,
        current_status: userProfile.details?.current_status,
        state: userProfile.details?.state,
        country: userProfile.details?.country,
        timezone: userProfile.details?.timezone,
        education_level: userProfile.details?.education_level,
        major_field_of_study: userProfile.details?.major_field_of_study,
        university_name: userProfile.details?.university_name,
        education_description: userProfile.details?.education_description,
        linkedin_link: userProfile.details?.linkedin_link,
        github_link: userProfile.details?.github_link,
        portfolio_link: userProfile.details?.portfolio_link,
        rating: userProfile.details?.rating,
        rating_count: userProfile.details?.rating_count
      };

      res.json({
        success: true,
        data: {
          user: userData
        }
      });

    } catch (error) {
      Logger.error('Get profile error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get profile',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }

  // Refresh token
  static async refreshToken(req, res) {
    try {
      const { user, token } = req;

      // Generate new token
      const newToken = JWTUtil.generateToken({
        userId: user.id,
        username: user.username,
        email: user.email
      });

      // Remove old session and create new one
      await JWTUtil.removeSession(user.id, token);
      
      const ipAddress = req.ip || req.connection.remoteAddress;
      const userAgent = req.get('User-Agent');
      await JWTUtil.storeSession(user.id, newToken, ipAddress, userAgent);

      res.json({
        success: true,
        message: 'Token refreshed successfully',
        data: {
          token: newToken
        }
      });

    } catch (error) {
      Logger.error('Refresh token error:', error);
      res.status(500).json({
        success: false,
        message: 'Token refresh failed',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }

  // Update user profile details
  static async updateProfile(req, res) {
    try {
      const userId = req.user.id;
      const {
        name,
        bio,
        current_status,
        state,
        country,
        timezone,
        education_level,
        major_field_of_study,
        university_name,
        education_description,
        linkedin_link,
        github_link,
        portfolio_link
      } = req.body;

      // Prepare update data (only include fields that are provided)
      const updateData = {};

      if (name !== undefined) updateData.name = name;
      if (bio !== undefined) updateData.bio = bio;
      if (current_status !== undefined) updateData.current_status = current_status;
      if (state !== undefined) updateData.state = state;
      if (country !== undefined) updateData.country = country;
      if (timezone !== undefined) updateData.timezone = timezone;
      if (education_level !== undefined) updateData.education_level = education_level;
      if (major_field_of_study !== undefined) updateData.major_field_of_study = major_field_of_study;
      if (university_name !== undefined) updateData.university_name = university_name;
      if (education_description !== undefined) updateData.education_description = education_description;
      if (linkedin_link !== undefined) updateData.linkedin_link = linkedin_link;
      if (github_link !== undefined) updateData.github_link = github_link;
      if (portfolio_link !== undefined) updateData.portfolio_link = portfolio_link;

      // Update user details (only if there are fields to update)
      if (Object.keys(updateData).length > 0) {
        await UserDetails.update(updateData, {
          where: { user_id: userId }
        });
      }

      // Get updated user data
      const updatedUser = await User.findByPk(userId, {
        include: [{
          model: UserDetails,
          as: 'details'
        }]
      });

      Logger.info(`User profile updated: ${updatedUser.username} (${updatedUser.email})`);

      // Format user data for response
      const userData = {
        id: updatedUser.id,
        username: updatedUser.username,
        email: updatedUser.email,
        status: updatedUser.status,
        is_admin: updatedUser.is_admin,
        email_verified: updatedUser.email_verified,
        name: updatedUser.details?.name,
        bio: updatedUser.details?.bio,
        current_status: updatedUser.details?.current_status,
        state: updatedUser.details?.state,
        country: updatedUser.details?.country,
        timezone: updatedUser.details?.timezone,
        education_level: updatedUser.details?.education_level,
        major_field_of_study: updatedUser.details?.major_field_of_study,
        university_name: updatedUser.details?.university_name,
        education_description: updatedUser.details?.education_description,
        linkedin_link: updatedUser.details?.linkedin_link,
        github_link: updatedUser.details?.github_link,
        portfolio_link: updatedUser.details?.portfolio_link,
        rating: updatedUser.details?.rating,
        rating_count: updatedUser.details?.rating_count
      };

      res.json({
        success: true,
        message: 'Profile updated successfully',
        data: { user: userData }
      });
    } catch (error) {
      Logger.error('Update profile error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  // Get user sessions
  static async getSessions(req, res) {
    try {
      const { user } = req;

      const sessions = await JWTUtil.getUserSessions(user.id);

      res.json({
        success: true,
        data: {
          sessions
        }
      });

    } catch (error) {
      Logger.error('Get sessions error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get sessions',
        error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
      });
    }
  }
}

module.exports = AuthController;
